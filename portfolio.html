<!DOCTYPE html>
<!--
    Página de portfólio otimizada para melhor desempenho:
    - Estrutura HTML simplificada
    - CSS sem animações
    - JavaScript simplificado sem animações
    - Carregamento assíncrono de recursos
    - Dimensões explícitas para imagens
    - Redução de projetos repetitivos
-->
<html lang="pt-BR">
<head>
    <meta charset="UTF-8" />
    <title>Portfólio - ASIMOV TECH SOLUTIONS</title>
    <meta name="description" content="Conheça nossos projetos e cases de sucesso em desenvolvimento de software, inteligência artificial e automação." />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <!-- Meta tags para otimização de desempenho -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="theme-color" content="#1a1a2e">
    <meta name="format-detection" content="telephone=no">
    <meta name="referrer" content="no-referrer-when-downgrade">
    <meta name="color-scheme" content="light">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/pages.css">
    <link rel="stylesheet" href="assets/css/portfolio-no-animations.css">
    <link rel="icon" href="assets/img/favicon/favicon.png" type="image/png">
    <meta property="og:image" content="img/banner.jpg">
    <meta property="og:image:type" content="image/jpeg">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <!-- Carregamento mínimo de fontes e ícones -->
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <!-- Carregando apenas os ícones essenciais para reduzir o tamanho -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/fontawesome.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/solid.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/brands.min.css">
    <!-- Carregando apenas os pesos de fonte necessários -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar">
        <div class="container navbar-container">
            <div class="nav-logo">
                <a href="index.html" class="logo-link">
                    <img src="assets/img/logo.png" alt="ASIMOV TECH SOLUTIONS Logo">
                    <div class="logo-text">
                        <span class="logo-primary">ASIMOV</span>
                        <span class="logo-secondary">TECH SOLUTIONS</span>
                    </div>
                </a>
            </div>

            <div class="nav-links">
                <a href="index.html" class="nav-link">
                    <i class="fas fa-home"></i>
                    <span>Início</span>
                </a>
                <div class="nav-dropdown">
                    <a href="portfolio.html" class="nav-link dropdown-toggle">
                        <i class="fas fa-box"></i>
                        <span>Produtos</span>
                        <i class="fas fa-chevron-down"></i>
                    </a>
                    <div class="dropdown-menu">
                        <a href="health.html" class="dropdown-link">
                            <i class="fas fa-heartbeat"></i>
                            <span>Asimov Health</span>
                        </a>
                        <a href="research.html" class="dropdown-link">
                            <i class="fas fa-atom"></i>
                            <span>Asimov Research</span>
                        </a>
                        <a href="iot.html" class="dropdown-link">
                            <i class="fas fa-city"></i>
                            <span>Asimov IoT & SmartCity</span>
                        </a>
                        <a href="fintech.html" class="dropdown-link">
                            <i class="fas fa-coins"></i>
                            <span>Asimov Fintech</span>
                        </a>
                        <a href="productivity.html" class="dropdown-link">
                            <i class="fas fa-tasks"></i>
                            <span>Asimov Productivity</span>
                        </a>
                        <a href="commerce.html" class="dropdown-link">
                            <i class="fas fa-shopping-cart"></i>
                            <span>Asimov Commerce</span>
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="juridico-contabil.html" class="dropdown-link">
                            <i class="fas fa-balance-scale"></i>
                            <span>ASIMOV Legal & Contábil</span>
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="portfolio.html" class="dropdown-link">
                            <i class="fas fa-project-diagram"></i>
                            <span>Todos os Produtos</span>
                        </a>
                    </div>
                </div>
                <a href="#" class="nav-link active">
                    <i class="fas fa-project-diagram"></i>
                    <span>Portfólio</span>
                </a>
                <a href="blog.html" class="nav-link">
                    <i class="fas fa-blog"></i>
                    <span>Blog</span>
                </a>
                <a href="carreiras.html" class="nav-link">
                    <i class="fas fa-briefcase"></i>
                    <span>Carreiras</span>
                </a>
                <a href="tecnologias.html" class="nav-link">
                    <i class="fas fa-microchip"></i>
                    <span>Tecnologias</span>
                </a>
                <a href="https://api.whatsapp.com/send/?phone=5521982301476" class="nav-button">
                    <i class="fas fa-paper-plane"></i>
                    <span>Iniciar Projeto</span>
                </a>
            </div>

            <button class="mobile-menu-button">
                <span></span>
                <span></span>
                <span></span>
            </button>
        </div>
    </nav>

    <!-- Menu Mobile -->
    <div class="mobile-menu">
        <a href="index.html" class="mobile-nav-link">
            <i class="fas fa-home"></i>
            <span>Início</span>
        </a>
        <a href="portfolio.html" class="mobile-nav-link">
            <i class="fas fa-box"></i>
            <span>Produtos</span>
        </a>
        <a href="portfolio.html" class="mobile-nav-link active">
            <i class="fas fa-project-diagram"></i>
            <span>Portfólio</span>
        </a>
        <a href="blog.html" class="mobile-nav-link">
            <i class="fas fa-blog"></i>
            <span>Blog</span>
        </a>
        <a href="carreiras.html" class="mobile-nav-link">
            <i class="fas fa-briefcase"></i>
            <span>Carreiras</span>
        </a>
        <a href="tecnologias.html" class="mobile-nav-link">
            <i class="fas fa-microchip"></i>
            <span>Tecnologias</span>
        </a>
        <a href="https://api.whatsapp.com/send/?phone=5521982301476&text&type=phone_number&app_absent=0" class="mobile-nav-link">
            <i class="fas fa-paper-plane"></i>
            <span>Iniciar Projeto</span>
        </a>
    </div>



    <!-- Introdução do Portfólio -->
    <section class="blog-header">
        <div class="container">
            <div class="header-content">
                <h1 class="main-title">Nosso <span class="highlight">Portfólio</span> Completo</h1>
                <p class="subtitle">Conheça nossos produtos SaaS organizados em verticais de negócio e os projetos customizados que desenvolvemos para nossos clientes.</p>
            </div>
        </div>
    </section>

    <!-- Nossos Produtos Section -->
    <section class="our-products">
        <div class="container">
            <div class="products-header">
                <h2 class="section-title">Nossos <span class="highlight">Produtos</span> SaaS</h2>
                <p class="section-subtitle">Conheça nosso portfólio de produtos SaaS organizados em 6 verticais de negócio</p>
            </div>

            <!-- Asimov IoT & SmartCity -->
            <div class="product-vertical">
                <div class="vertical-header">
                    <div class="vertical-icon">
                        <i class="fas fa-city"></i>
                    </div>
                    <div class="vertical-info">
                        <h3 class="vertical-title">Asimov IoT & SmartCity</h3>
                        <p class="vertical-description">Soluções para cidades inteligentes e monitoramento IoT</p>
                    </div>
                </div>
                <div class="products-grid">
                    <div class="product-card">
                        <div class="product-header">
                            <h4 class="product-name">EnviroMesh</h4>
                            <span class="product-status">Em Desenvolvimento</span>
                        </div>
                        <p class="product-description">Rede adaptativa que faz ingestão e fusão de dados de sensores ambientais (ar, ruído, tráfego) em tempo real com dashboard React.</p>
                        <div class="product-tech">
                            <span class="tech-tag">React</span>
                            <span class="tech-tag">IoT</span>
                            <span class="tech-tag">Real-time</span>
                            <span class="tech-tag">Sensors</span>
                        </div>
                    </div>
                    <div class="product-card">
                        <div class="product-header">
                            <h4 class="product-name">Foresight</h4>
                            <span class="product-status">Ativo</span>
                        </div>
                        <p class="product-description">Framework Go que usa ALMA + RSI + ATR para prever picos de métricas de infraestrutura e acionar ações pró-ativas.</p>
                        <div class="product-tech">
                            <span class="tech-tag">Go</span>
                            <span class="tech-tag">Monitoring</span>
                            <span class="tech-tag">Predictive</span>
                            <span class="tech-tag">Infrastructure</span>
                        </div>
                    </div>
                    <div class="product-card">
                        <div class="product-header">
                            <h4 class="product-name">Foresight Cloud</h4>
                            <span class="product-status">Em Desenvolvimento</span>
                        </div>
                        <p class="product-description">Versão SaaS multi-tenant do Foresight com API gateway Node.js, engine Go e dashboards preditivos.</p>
                        <div class="product-tech">
                            <span class="tech-tag">Node.js</span>
                            <span class="tech-tag">Go</span>
                            <span class="tech-tag">Multi-tenant</span>
                            <span class="tech-tag">Cloud</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Asimov Fintech -->
            <div class="product-vertical">
                <div class="vertical-header">
                    <div class="vertical-icon">
                        <i class="fas fa-coins"></i>
                    </div>
                    <div class="vertical-info">
                        <h3 class="vertical-title">Asimov Fintech</h3>
                        <p class="vertical-description">Soluções financeiras e trading algorítmico</p>
                    </div>
                </div>
                <div class="products-grid">
                    <div class="product-card">
                        <div class="product-header">
                            <h4 class="product-name">CryptoSignals</h4>
                            <span class="product-status featured">Produto Herói</span>
                        </div>
                        <p class="product-description">"Bloomberg de criptos": análise técnica avançada, predições IA multi-timeframe, backtesting e alertas multi-rede.</p>
                        <div class="product-tech">
                            <span class="tech-tag">React</span>
                            <span class="tech-tag">Python</span>
                            <span class="tech-tag">TensorFlow</span>
                            <span class="tech-tag">WebSocket</span>
                        </div>
                    </div>
                    <div class="product-card">
                        <div class="product-header">
                            <h4 class="product-name">PlasmaSync DB</h4>
                            <span class="product-status">Beta</span>
                        </div>
                        <p class="product-description">Banco de dados de eventos imutáveis com replicação P2P e trocas atômicas Bitcoin–Nostr via TANOS.</p>
                        <div class="product-tech">
                            <span class="tech-tag">Rust</span>
                            <span class="tech-tag">P2P</span>
                            <span class="tech-tag">Bitcoin</span>
                            <span class="tech-tag">Nostr</span>
                        </div>
                    </div>
                    <div class="product-card">
                        <div class="product-header">
                            <h4 class="product-name">JarvisTrade Lite</h4>
                            <span class="product-status">Ativo</span>
                        </div>
                        <p class="product-description">Bot local de scalping na Binance que detecta manipulação de orderbook com XGBoost e executa ordens em milissegundos.</p>
                        <div class="product-tech">
                            <span class="tech-tag">Python</span>
                            <span class="tech-tag">XGBoost</span>
                            <span class="tech-tag">Binance API</span>
                            <span class="tech-tag">Real-time</span>
                        </div>
                    </div>
                    <div class="product-card">
                        <div class="product-header">
                            <h4 class="product-name">ALMA Trading System</h4>
                            <span class="product-status">Ativo</span>
                        </div>
                        <p class="product-description">Sistema algorítmico para Binance Futures com estratégia ALMA dupla, stop-loss dinâmico e parser de sinais Telegram.</p>
                        <div class="product-tech">
                            <span class="tech-tag">Python</span>
                            <span class="tech-tag">ALMA</span>
                            <span class="tech-tag">Futures</span>
                            <span class="tech-tag">Telegram</span>
                        </div>
                    </div>
                    <div class="product-card">
                        <div class="product-header">
                            <h4 class="product-name">Flash Compliance</h4>
                            <span class="product-status">Beta</span>
                        </div>
                        <p class="product-description">Serviço serverless que verifica trocas atômicas Bitcoin–Nostr e gera relatórios regulatórios via Gemini AI.</p>
                        <div class="product-tech">
                            <span class="tech-tag">Serverless</span>
                            <span class="tech-tag">Gemini AI</span>
                            <span class="tech-tag">Compliance</span>
                            <span class="tech-tag">Bitcoin</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Asimov Productivity -->
            <div class="product-vertical">
                <div class="vertical-header">
                    <div class="vertical-icon">
                        <i class="fas fa-tasks"></i>
                    </div>
                    <div class="vertical-info">
                        <h3 class="vertical-title">Asimov Productivity</h3>
                        <p class="vertical-description">Ferramentas de produtividade e análise de documentos</p>
                    </div>
                </div>
                <div class="products-grid">
                    <div class="product-card">
                        <div class="product-header">
                            <h4 class="product-name">DocGenie</h4>
                            <span class="product-status">Em Desenvolvimento</span>
                        </div>
                        <p class="product-description">Assistente IA para análise de documentos jurídicos/contábeis que gera resumos, checklist de riscos e narração em áudio.</p>
                        <div class="product-tech">
                            <span class="tech-tag">NLP</span>
                            <span class="tech-tag">OpenAI</span>
                            <span class="tech-tag">Audio</span>
                            <span class="tech-tag">Legal Tech</span>
                        </div>
                    </div>
                    <div class="product-card">
                        <div class="product-header">
                            <h4 class="product-name">EchoPilot</h4>
                            <span class="product-status">Beta</span>
                        </div>
                        <p class="product-description">Mensageria em vídeo com transcrição automática, análise de sentimento e resumos IA, construída sobre LiveKit.</p>
                        <div class="product-tech">
                            <span class="tech-tag">LiveKit</span>
                            <span class="tech-tag">WebRTC</span>
                            <span class="tech-tag">Speech-to-Text</span>
                            <span class="tech-tag">Sentiment Analysis</span>
                        </div>
                    </div>
                    <div class="product-card">
                        <div class="product-header">
                            <h4 class="product-name">Exo Piper CLI</h4>
                            <span class="product-status">Ativo</span>
                        </div>
                        <p class="product-description">Ferramenta de auditoria de performance que calcula λ e p (Teorema da Relatividade de Complexidade) e gera relatórios de benchmark.</p>
                        <div class="product-tech">
                            <span class="tech-tag">CLI</span>
                            <span class="tech-tag">Performance</span>
                            <span class="tech-tag">Benchmark</span>
                            <span class="tech-tag">Analytics</span>
                        </div>
                    </div>
                    <div class="product-card">
                        <div class="product-header">
                            <h4 class="product-name">Exo Piper SaaS</h4>
                            <span class="product-status">Em Desenvolvimento</span>
                        </div>
                        <p class="product-description">Plataforma self-service de auditoria de performance com backend FastAPI/Celery, dashboard Next.js e agente Docker.</p>
                        <div class="product-tech">
                            <span class="tech-tag">FastAPI</span>
                            <span class="tech-tag">Celery</span>
                            <span class="tech-tag">Next.js</span>
                            <span class="tech-tag">Docker</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Asimov Commerce & Engage -->
            <div class="product-vertical">
                <div class="vertical-header">
                    <div class="vertical-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="vertical-info">
                        <h3 class="vertical-title">Asimov Commerce & Engage</h3>
                        <p class="vertical-description">Soluções para e-commerce e engajamento de clientes</p>
                    </div>
                </div>
                <div class="products-grid">
                    <div class="product-card">
                        <div class="product-header">
                            <h4 class="product-name">TeemoFlow</h4>
                            <span class="product-status">Em Desenvolvimento</span>
                        </div>
                        <p class="product-description">Plataforma de marketing para WhatsApp: campanhas, fluxos n8n, personalização IA e analytics em tempo real.</p>
                        <div class="product-tech">
                            <span class="tech-tag">WhatsApp API</span>
                            <span class="tech-tag">n8n</span>
                            <span class="tech-tag">IA</span>
                            <span class="tech-tag">Analytics</span>
                        </div>
                    </div>
                    <div class="product-card">
                        <div class="product-header">
                            <h4 class="product-name">MenuFlow.AI</h4>
                            <span class="product-status">Beta</span>
                        </div>
                        <p class="product-description">Cardápio digital com integração direta ao WhatsApp, dashboard administrativo e notificações de pedidos.</p>
                        <div class="product-tech">
                            <span class="tech-tag">React</span>
                            <span class="tech-tag">WhatsApp</span>
                            <span class="tech-tag">QR Code</span>
                            <span class="tech-tag">Food Tech</span>
                        </div>
                    </div>
                    <div class="product-card">
                        <div class="product-header">
                            <h4 class="product-name">Nexlify Contábil</h4>
                            <span class="product-status">Ativo</span>
                        </div>
                        <p class="product-description">Gestão de documentos e processamento contábil online com múltiplos planos de assinatura e integrações.</p>
                        <div class="product-tech">
                            <span class="tech-tag">SaaS</span>
                            <span class="tech-tag">Accounting</span>
                            <span class="tech-tag">Document Management</span>
                            <span class="tech-tag">Integrations</span>
                        </div>
                    </div>
                    <div class="product-card">
                        <div class="product-header">
                            <h4 class="product-name">ASIMOV Network</h4>
                            <span class="product-status">Beta</span>
                        </div>
                        <p class="product-description">Terminal web cyberpunk para role-play comunitário, votações on-chain e colaboração de código em tempo real.</p>
                        <div class="product-tech">
                            <span class="tech-tag">Web3</span>
                            <span class="tech-tag">Blockchain</span>
                            <span class="tech-tag">Real-time</span>
                            <span class="tech-tag">Community</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Asimov Health -->
            <div class="product-vertical">
                <div class="vertical-header">
                    <div class="vertical-icon">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <div class="vertical-info">
                        <h3 class="vertical-title">Asimov Health</h3>
                        <p class="vertical-description">Soluções para profissionais de saúde e bem-estar</p>
                    </div>
                </div>
                <div class="products-grid">
                    <div class="product-card">
                        <div class="product-header">
                            <h4 class="product-name">Hypatium</h4>
                            <span class="product-status">Em Desenvolvimento</span>
                        </div>
                        <p class="product-description">Plataforma integrada para personal trainers, nutricionistas e psicólogos gerirem avaliações físicas, planos de treino, dieta e relatórios IA em um único painel.</p>
                        <div class="product-tech">
                            <span class="tech-tag">React</span>
                            <span class="tech-tag">Node.js</span>
                            <span class="tech-tag">PostgreSQL</span>
                            <span class="tech-tag">IA</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Asimov Research -->
            <div class="product-vertical">
                <div class="vertical-header">
                    <div class="vertical-icon">
                        <i class="fas fa-atom"></i>
                    </div>
                    <div class="vertical-info">
                        <h3 class="vertical-title">Asimov Research</h3>
                        <p class="vertical-description">Ferramentas para pesquisa científica e computação quântica</p>
                    </div>
                </div>
                <div class="products-grid">
                    <div class="product-card">
                        <div class="product-header">
                            <h4 class="product-name">Quantum Protocol Engineering Assistant</h4>
                            <span class="product-status">Beta</span>
                        </div>
                        <p class="product-description">Web-app que converte descrições em linguagem natural de protocolos de correção de erros quânticos em código Qiskit pronto para teste.</p>
                        <div class="product-tech">
                            <span class="tech-tag">Python</span>
                            <span class="tech-tag">Qiskit</span>
                            <span class="tech-tag">NLP</span>
                            <span class="tech-tag">Quantum</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="products-cta">
                <div class="cta-content">
                    <h3>Interessado em nossos produtos?</h3>
                    <p>Entre em contato para saber mais sobre nossos produtos SaaS e como eles podem transformar seu negócio</p>
                    <a href="https://api.whatsapp.com/send/?phone=5521982301476" class="button button-primary">
                        <i class="fas fa-comments"></i>
                        <span>Falar com Especialista</span>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Projetos Customizados -->
    <section class="custom-projects-header">
        <div class="container">
            <div class="header-content">
                <h2 class="section-title">Projetos <span class="highlight">Customizados</span></h2>
                <p class="section-subtitle">Conheça alguns dos projetos customizados que desenvolvemos para nossos clientes</p>
            </div>
        </div>
    </section>

    <!-- Lista de Projetos -->
    <section class="portfolio-projects">
        <div class="container">
            <div class="custom-projects-grid">
                <!-- Projeto CryptoSignals -->
                <div class="custom-project-card" data-category="web,ai">
                    <div class="project-header">
                        <div class="project-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="project-status">Concluído</div>
                    </div>
                    <h3 class="project-name">CryptoSignals</h3>
                    <p class="project-description">Plataforma de sinais de trading com análise técnica avançada e inteligência artificial para criptomoedas.</p>
                    <div class="project-tech">
                        <span class="tech-tag">React</span>
                        <span class="tech-tag">Node.js</span>
                        <span class="tech-tag">Python</span>
                        <span class="tech-tag">TensorFlow</span>
                    </div>
                    <a href="https://cryptosignals.me" target="_blank" class="project-link">
                        Ver Projeto <i class="fas fa-external-link-alt"></i>
                    </a>
                </div>

                <!-- Projeto GTReid -->
                <div class="custom-project-card" data-category="web">
                    <div class="project-header">
                        <div class="project-icon">
                            <i class="fas fa-globe"></i>
                        </div>
                        <div class="project-status">Concluído</div>
                    </div>
                    <h3 class="project-name">GTReid</h3>
                    <p class="project-description">Desenvolvimento front-end para o site institucional da GTReid, com design responsivo e otimizado para conversão.</p>
                    <div class="project-tech">
                        <span class="tech-tag">HTML5</span>
                        <span class="tech-tag">CSS3</span>
                        <span class="tech-tag">JavaScript</span>
                        <span class="tech-tag">Responsive</span>
                    </div>
                    <span class="project-link disabled">
                        Projeto Privado <i class="fas fa-lock"></i>
                    </span>
                </div>

                <!-- Projeto Jeep Tour -->
                <div class="custom-project-card" data-category="web">
                    <div class="project-header">
                        <div class="project-icon">
                            <i class="fas fa-mountain"></i>
                        </div>
                        <div class="project-status">Concluído</div>
                    </div>
                    <h3 class="project-name">Jeep Tour</h3>
                    <p class="project-description">Site em WordPress para empresa de turismo de aventura, com sistema de reservas online e galeria de experiências.</p>
                    <div class="project-tech">
                        <span class="tech-tag">WordPress</span>
                        <span class="tech-tag">PHP</span>
                        <span class="tech-tag">HTML5</span>
                        <span class="tech-tag">CSS3</span>
                    </div>
                    <a href="http://jeeptour.com.br/" target="_blank" class="project-link">
                        Ver Projeto <i class="fas fa-external-link-alt"></i>
                    </a>
                </div>

                <!-- Projeto Eunápolis -->
                <div class="custom-project-card" data-category="web">
                    <div class="project-header">
                        <div class="project-icon">
                            <i class="fas fa-landmark"></i>
                        </div>
                        <div class="project-status">Concluído</div>
                    </div>
                    <h3 class="project-name">Prefeitura de Eunápolis</h3>
                    <p class="project-description">Portal oficial da Prefeitura de Eunápolis, com foco em transparência e acesso à informação para os cidadãos.</p>
                    <div class="project-tech">
                        <span class="tech-tag">WordPress</span>
                        <span class="tech-tag">HTML5</span>
                        <span class="tech-tag">CSS3</span>
                        <span class="tech-tag">JavaScript</span>
                    </div>
                    <a href="http://eunapolis.ba.gov.br/" target="_blank" class="project-link">
                        Ver Projeto <i class="fas fa-external-link-alt"></i>
                    </a>
                </div>

                <!-- Projeto Trinks -->
                <div class="custom-project-card" data-category="web">
                    <div class="project-header">
                        <div class="project-icon">
                            <i class="fas fa-utensils"></i>
                        </div>
                        <div class="project-status">Concluído</div>
                    </div>
                    <h3 class="project-name">Trinks</h3>
                    <p class="project-description">Plataforma de reservas em restaurantes com interface intuitiva e responsiva, integrada com back-end ASP.NET.</p>
                    <div class="project-tech">
                        <span class="tech-tag">ASP.NET</span>
                        <span class="tech-tag">HTML5</span>
                        <span class="tech-tag">CSS3</span>
                        <span class="tech-tag">JavaScript</span>
                    </div>
                    <a href="https://www.trinks.com/" target="_blank" class="project-link">
                        Ver Projeto <i class="fas fa-external-link-alt"></i>
                    </a>
                </div>

                <!-- Projeto Sistema VM -->
                <div class="custom-project-card" data-category="web,automation">
                    <div class="project-header">
                        <div class="project-icon">
                            <i class="fas fa-server"></i>
                        </div>
                        <div class="project-status">Concluído</div>
                    </div>
                    <h3 class="project-name">Sistema de Gerenciamento VM</h3>
                    <p class="project-description">Plataforma de gerenciamento de máquinas virtuais com integração SSH e monitoramento em tempo real.</p>
                    <div class="project-tech">
                        <span class="tech-tag">Node.js</span>
                        <span class="tech-tag">React</span>
                        <span class="tech-tag">PostgreSQL</span>
                        <span class="tech-tag">Docker</span>
                    </div>
                    <span class="project-link disabled">
                        Projeto Privado <i class="fas fa-lock"></i>
                    </span>
                </div>

                <!-- Projeto Porto Seguro -->
                <div class="custom-project-card" data-category="web">
                    <div class="project-header">
                        <div class="project-icon">
                            <i class="fas fa-shield-virus"></i>
                        </div>
                        <div class="project-status">Concluído</div>
                    </div>
                    <h3 class="project-name">Porto Seguro - Todos Contra o Aedes</h3>
                    <p class="project-description">Site para campanha de saúde pública da Prefeitura de Porto Seguro, com informações sobre prevenção e combate ao Aedes aegypti.</p>
                    <div class="project-tech">
                        <span class="tech-tag">WordPress</span>
                        <span class="tech-tag">HTML5</span>
                        <span class="tech-tag">CSS3</span>
                        <span class="tech-tag">JavaScript</span>
                    </div>
                    <span class="project-link disabled">
                        Projeto Privado <i class="fas fa-lock"></i>
                    </span>
                </div>

                <!-- Projeto SOS Comunidades -->
                <div class="custom-project-card" data-category="web">
                    <div class="project-header">
                        <div class="project-icon">
                            <i class="fas fa-hands-helping"></i>
                        </div>
                        <div class="project-status">Concluído</div>
                    </div>
                    <h3 class="project-name">SOS Comunidades</h3>
                    <p class="project-description">Sistema de gestão para projetos sociais com módulos de cadastro de beneficiários, controle de doações e relatórios.</p>
                    <div class="project-tech">
                        <span class="tech-tag">ASP.NET</span>
                        <span class="tech-tag">C#</span>
                        <span class="tech-tag">SQL Server</span>
                        <span class="tech-tag">JavaScript</span>
                    </div>
                    <span class="project-link disabled">
                        Projeto Privado <i class="fas fa-lock"></i>
                    </span>
                </div>

                <!-- Projeto Sistema Veterinário -->
                <div class="custom-project-card" data-category="web,ai">
                    <div class="project-header">
                        <div class="project-icon">
                            <i class="fas fa-cow"></i>
                        </div>
                        <div class="project-status">Concluído</div>
                    </div>
                    <h3 class="project-name">Sistema de Gestão Veterinária</h3>
                    <p class="project-description">Plataforma especializada para administração de rebanho e procedimentos veterinários com recursos avançados de gestão.</p>
                    <div class="project-tech">
                        <span class="tech-tag">Laravel</span>
                        <span class="tech-tag">React</span>
                        <span class="tech-tag">MySQL</span>
                        <span class="tech-tag">Docker</span>
                    </div>
                    <span class="project-link disabled">
                        Projeto Privado <i class="fas fa-lock"></i>
                    </span>
                </div>

                <!-- Projeto Profarma -->
                <div class="custom-project-card" data-category="web">
                    <div class="project-header">
                        <div class="project-icon">
                            <i class="fas fa-pills"></i>
                        </div>
                        <div class="project-status">Concluído</div>
                    </div>
                    <h3 class="project-name">Profarma</h3>
                    <p class="project-description">Plataforma ASP.NET para gestão de farmácias, incluindo controle de estoque, vendas e relatórios financeiros.</p>
                    <div class="project-tech">
                        <span class="tech-tag">ASP.NET</span>
                        <span class="tech-tag">C#</span>
                        <span class="tech-tag">SQL Server</span>
                        <span class="tech-tag">JavaScript</span>
                    </div>
                    <span class="project-link disabled">
                        Projeto Privado <i class="fas fa-lock"></i>
                    </span>
                </div>

                <!-- Projeto Mackenzie -->
                <div class="custom-project-card" data-category="web,education">
                    <div class="project-header">
                        <div class="project-icon">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <div class="project-status">Concluído</div>
                    </div>
                    <h3 class="project-name">Mackenzie</h3>
                    <p class="project-description">Customização de ambiente Moodle para a Universidade Mackenzie, integrando identidade visual e trilhas de aprendizado.</p>
                    <div class="project-tech">
                        <span class="tech-tag">PHP</span>
                        <span class="tech-tag">Moodle</span>
                        <span class="tech-tag">MySQL</span>
                        <span class="tech-tag">JavaScript</span>
                    </div>
                    <span class="project-link disabled">
                        Projeto Privado <i class="fas fa-lock"></i>
                    </span>
                </div>

                <!-- Projeto Educação Corporativa -->
                <div class="custom-project-card" data-category="web,education">
                    <div class="project-header">
                        <div class="project-icon">
                            <i class="fas fa-chalkboard-teacher"></i>
                        </div>
                        <div class="project-status">Concluído</div>
                    </div>
                    <h3 class="project-name">Plataformas de Educação Corporativa</h3>
                    <p class="project-description">Desenvolvimento de ambientes virtuais de aprendizagem para diversas empresas como TIM Connect, RNP, Unissuam, IPOG e Enauta.</p>
                    <div class="project-tech">
                        <span class="tech-tag">PHP</span>
                        <span class="tech-tag">Moodle</span>
                        <span class="tech-tag">MySQL</span>
                        <span class="tech-tag">JavaScript</span>
                    </div>
                    <span class="project-link disabled">
                        Projetos Privados <i class="fas fa-lock"></i>
                    </span>
                </div>

                <!-- Projeto Youpluv -->
                <div class="custom-project-card" data-category="web">
                    <div class="project-header">
                        <div class="project-icon">
                            <i class="fas fa-cloud-rain"></i>
                        </div>
                        <div class="project-status">Concluído</div>
                    </div>
                    <h3 class="project-name">Youpluv</h3>
                    <p class="project-description">API REST para sistema de monitoramento pluviométrico colaborativo. Trabalho de Conclusão de Curso com foco em sustentabilidade.</p>
                    <div class="project-tech">
                        <span class="tech-tag">PHP</span>
                        <span class="tech-tag">REST API</span>
                        <span class="tech-tag">MySQL</span>
                        <span class="tech-tag">JavaScript</span>
                    </div>
                    <span class="project-link disabled">
                        Projeto Privado <i class="fas fa-lock"></i>
                    </span>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="portfolio-cta">
        <div class="container">
            <div class="cta-content">
                <h2>Pronto para Transformar sua Ideia em Realidade?</h2>
                <p>Entre em contato conosco e vamos discutir como podemos ajudar seu negócio a crescer</p>
                <a href="https://api.whatsapp.com/send/?phone=5521982301476" class="button button-primary">
                    <i class="fas fa-rocket"></i>
                    <span>Iniciar Projeto</span>
                </a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-grid">
                <!-- Coluna 1: Sobre -->
                <div class="footer-column">
                    <div class="footer-brand">
                        <img src="assets/img/logo.png" alt="ASIMOV TECH SOLUTIONS Logo" class="footer-logo" width="40" height="40" loading="lazy">
                        <h3>ASIMOV TECH</h3>
                    </div>
                    <p class="footer-description">Transformando o futuro através da inovação tecnológica. Soluções personalizadas para impulsionar seu negócio.</p>
                    <div class="footer-social">
                        <a href="https://www.linkedin.com/company/asimovink" class="social-link" aria-label="LinkedIn">
                            <i class="fab fa-linkedin"></i>
                        </a>
                        <a href="https://github.com/asimovtechsolutions" class="social-link" aria-label="GitHub">
                            <i class="fab fa-github"></i>
                        </a>
                        <a href="https://api.whatsapp.com/send/?phone=5521982301476" class="social-link" aria-label="WhatsApp">
                            <i class="fab fa-whatsapp"></i>
                        </a>
                        <a href="https://instagram.com/asimovtech.systems" class="social-link" aria-label="Instagram">
                            <i class="fab fa-instagram"></i>
                        </a>
                    </div>
                </div>

                <!-- Coluna 2: Links Rápidos -->
                <div class="footer-column">
                    <h4 class="footer-title">Links Rápidos</h4>
                    <ul class="footer-links">
                        <li><a href="index.html#sobre">Sobre Nós</a></li>
                        <li><a href="index.html#servicos">Nossos Serviços</a></li>
                        <li><a href="portfolio.html">Portfólio</a></li>
                        <li><a href="blog.html">Blog</a></li>
                        <li><a href="carreiras.html">Carreiras</a></li>
                        <li><a href="tecnologias.html">Tecnologias</a></li>
                        <li><a href="index.html#cases">Cases de Sucesso</a></li>
                        <li><a href="index.html#contato">Fale Conosco</a></li>
                    </ul>
                </div>

                <!-- Coluna 3: Serviços -->
                <div class="footer-column">
                    <h4 class="footer-title">Nossos Serviços</h4>
                    <ul class="footer-links">
                        <li><a href="index.html#servicos">Desenvolvimento de Software</a></li>
                        <li><a href="index.html#servicos">Inteligência Artificial</a></li>
                        <li><a href="index.html#servicos">Automação de Processos</a></li>
                        <li><a href="index.html#servicos">Consultoria em TI</a></li>
                        <li><a href="index.html#servicos">Cloud Computing</a></li>
                    </ul>
                </div>

                <!-- Coluna 4: Contato -->
                <div class="footer-column">
                    <h4 class="footer-title">Contato</h4>
                    <ul class="footer-contact">
                        <li>
                            <i class="fas fa-phone"></i>
                            <span>+55 21 98230-1476</span>
                        </li>
                        <li>
                            <i class="fas fa-envelope"></i>
                            <span><EMAIL></span>
                        </li>
                        <li>
                            <i class="fas fa-map-marker-alt"></i>
                            <span>Rio de Janeiro, RJ</span>
                        </li>
                        <li>
                            <i class="fas fa-clock"></i>
                            <span>Seg-Sex: 9h às 18h</span>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="footer-bottom">
                <div class="footer-copyright">
                    <p>&copy; 2024 ASIMOV TECH SOLUTIONS. Todos os direitos reservados.</p>
                </div>
                <div class="footer-legal">
                    <a href="privacidade.html">Política de Privacidade</a>
                    <a href="termos.html">Termos de Uso</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts simplificados sem animações -->
    <script src="assets/js/portfolio-no-animations.js" defer></script>
    <script src="assets/js/structured-data.js" async></script>

    <!-- Google tag (gtag.js) - carregamento mínimo -->
    <script>
      window.addEventListener('load', function() {
        var script = document.createElement('script');
        script.src = 'https://www.googletagmanager.com/gtag/js?id=G-XXXXXXXXXX';
        script.async = true;
        document.body.appendChild(script);

        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-XXXXXXXXXX');
      });
    </script>
</body>
</html>
