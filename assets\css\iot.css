/* ===== ASIMOV IOT & SMARTCITY STYLES - BLUE/GREEN THEME ===== */

/* Override ASIMOV logo colors to use blue theme */
.navbar .logo-primary,
.footer .logo-primary {
    color: var(--asimov-blue) !important;
}

/* Force center alignment for section titles - Override style.css conflicts */
#enviromesh .section-title,
.iot-products .section-title,
.product-hero-header .section-title,
.pricing-section .section-title,
.products-grid-section .section-title,
.section-title {
    text-align: center !important;
    margin-left: auto !important;
    margin-right: auto !important;
    display: block !important;
    width: 100% !important;
    left: auto !important;
    transform: none !important;
    position: relative !important;
}

/* Ensure containers are also centered */
.product-hero-header,
.iot-products .section-header,
.pricing-section .section-header,
.products-grid-section .section-header,
.section-header {
    text-align: center !important;
    width: 100% !important;
    display: block !important;
}

:root {
    /* IoT Color Palette - Smart City Blue & Green Theme */
    --iot-primary: #2D7FF9;                 /* ASIMOV Blue - Primary Urban */
    --iot-secondary: #1C57B5;               /* Dark Urban Blue */
    --iot-accent: #00C896;                  /* Sustainable Green - Environment */
    --iot-light: #F8FBFF;                   /* Urban White Background */
    --iot-dark: #2C3E50;                    /* Infrastructure Dark Text */
    --iot-text-light: #FFFFFF;              /* Pure White Text */
    --iot-text-muted: #7F8C8D;              /* Urban Muted Text */
    --iot-soft-blue: #74B9FF;               /* Soft Urban Blue */
    --iot-eco-green: #00E676;               /* Eco Green for highlights */

    /* ASIMOV Brand Colors - Consistent */
    --asimov-blue: #2D7FF9;                 /* Primary Blue for ASIMOV logo */
    --asimov-blue-dark: #1C57B5;            /* Darker Blue */

    /* Smart City Gradients */
    --iot-gradient-primary: linear-gradient(135deg, #2D7FF9 0%, #1C57B5 100%);
    --iot-gradient-light: linear-gradient(135deg, #F8FBFF 0%, #E3F2FD 100%);
    --iot-gradient-accent: linear-gradient(135deg, #00C896 0%, #2D7FF9 100%);
    --iot-gradient-eco: linear-gradient(135deg, #00E676 0%, #00C896 100%);
}

/* Mobile menu styles for iot page */
.mobile-menu {
    position: fixed;
    top: 80px;
    left: 0;
    right: 0;
    background: white;
    padding: 1rem;
    border-bottom: 1px solid var(--neutral-200);
    transform: translateY(-100%);
    transition: transform 0.3s ease;
    z-index: 999;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.mobile-menu.active {
    transform: translateY(0);
}

.mobile-nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    color: var(--neutral-700);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border-radius: 8px;
    margin-bottom: 0.5rem;
}

.mobile-nav-link:hover {
    background: var(--primary-light);
    color: var(--primary-color);
}

.mobile-nav-link i {
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

.mobile-menu-button {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
}

.mobile-menu-button span {
    width: 25px;
    height: 3px;
    background: var(--neutral-700);
    border-radius: 2px;
    transition: all 0.3s ease;
}

.mobile-menu-button.active span:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-button.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-button.active span:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* IoT Hero Section Enhancements */
.iot-hero {
    background: var(--iot-gradient-primary);
    position: relative;
    overflow: hidden;
}

.iot-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="iot-grid" width="12" height="12" patternUnits="userSpaceOnUse"><circle cx="6" cy="6" r="1" fill="rgba(0,200,150,0.2)"/><path d="M 0 6 L 12 6 M 6 0 L 6 12" stroke="rgba(0,200,150,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23iot-grid)"/></svg>');
    opacity: 0.4;
}

.iot-hero .hero-description {
    font-size: 1.25rem;
    line-height: 1.6;
    margin-bottom: 3rem;
    max-width: 600px;
    opacity: 1;
    text-align: center;
    margin-left: auto;
    margin-right: auto;
    color: white;
    font-weight: 400;
}

/* Product Hero Section */
.product-hero {
    background: var(--iot-light);
    padding: 6rem 0;
}

.product-hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

/* IoT Dashboard Enhancements */
.iot-dashboard {
    padding: 2rem;
    background: white;
    border-radius: 16px;
    border: 2px solid var(--iot-accent);
    box-shadow: 0 10px 30px rgba(0, 200, 150, 0.15);
}

.sensor-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.sensor-card {
    background: var(--iot-light);
    padding: 1rem;
    border-radius: 12px;
    border: 1px solid var(--neutral-200);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    position: relative;
}

.sensor-icon {
    width: 40px;
    height: 40px;
    background: var(--iot-gradient-accent);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.sensor-data {
    flex: 1;
}

.sensor-value {
    display: block;
    font-weight: 700;
    font-size: 1.1rem;
    color: var(--iot-primary);
}

.sensor-label {
    display: block;
    font-size: 0.8rem;
    color: var(--iot-text-muted);
}

.sensor-status {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
}

.sensor-status.good {
    background: var(--iot-accent);
    box-shadow: 0 0 8px rgba(0, 200, 150, 0.4);
}

.sensor-status.warning {
    background: #ffa500;
}

.map-preview {
    background: var(--iot-light);
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    border: 1px solid var(--neutral-200);
}

.map-placeholder {
    color: var(--iot-secondary);
    font-size: 1.2rem;
}

.map-placeholder i {
    display: block;
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

/* Pricing Section Enhancements */
.pricing-section {
    background: var(--iot-gradient-light);
    padding: 6rem 0;
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 3rem;
}

.pricing-card {
    background: white;
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow: 0 10px 30px rgba(45, 127, 249, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(45, 127, 249, 0.1);
}

.pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 200, 150, 0.2);
    border-color: var(--iot-accent);
}

.pricing-card.featured {
    border: 2px solid var(--iot-accent);
    transform: scale(1.05);
}

.pricing-card.featured:hover {
    transform: scale(1.05) translateY(-10px);
}

.pricing-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--iot-gradient-accent);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

/* Products Grid Section */
.products-grid-section {
    background: white;
    padding: 6rem 0;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.product-card {
    background: white;
    border: 1px solid var(--neutral-200);
    border-radius: 16px;
    padding: 2rem;
    transition: all 0.3s ease;
    position: relative;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 200, 150, 0.15);
    border-color: var(--iot-accent);
}

.product-icon {
    width: 60px;
    height: 60px;
    background: var(--iot-gradient-accent);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.product-icon i {
    font-size: 1.5rem;
    color: white;
}

/* Status badges */
.status-badge.active {
    background: #dcfce7;
    color: #166534;
}

.status-badge.beta {
    background: #dbeafe;
    color: #1e40af;
}

/* CTA Section */
.cta-section {
    background: var(--iot-gradient-primary);
    color: white;
    padding: 6rem 0;
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
}

.cta-content p {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Button Styles */
.button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    text-align: center;
    justify-content: center;
}

.button-primary {
    background: var(--iot-accent);
    color: white;
    box-shadow: 0 4px 15px rgba(0, 200, 150, 0.3);
}

.button-primary:hover {
    background: var(--iot-primary);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(45, 127, 249, 0.4);
}

.button-secondary {
    background: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.button-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

.button-outline {
    background: transparent;
    color: var(--iot-accent);
    border: 2px solid var(--iot-accent);
}

.button-outline:hover {
    background: var(--iot-accent);
    color: var(--iot-primary);
    border-color: var(--iot-accent);
}

.button i {
    font-size: 1.1rem;
}

/* Pricing Section Button Overrides */
.pricing-card .button-outline {
    background: transparent;
    color: var(--iot-accent);
    border: 2px solid var(--iot-accent);
}

.pricing-card .button-outline:hover {
    background: var(--iot-accent);
    color: var(--iot-primary);
    border-color: var(--iot-accent);
}

.pricing-card .button-primary {
    background: var(--iot-accent);
    color: white;
    box-shadow: 0 4px 15px rgba(0, 200, 150, 0.3);
}

.pricing-card .button-primary:hover {
    background: var(--iot-primary);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(45, 127, 249, 0.4);
}

/* IoT Page Specific Elements */
.product-badge {
    background: var(--iot-gradient-accent) !important;
    color: white !important;
}

.status-badge.active {
    background: rgba(0, 200, 150, 0.1) !important;
    color: var(--iot-accent) !important;
    border: 1px solid rgba(0, 200, 150, 0.2);
}

.status-badge.beta {
    background: rgba(45, 127, 249, 0.1) !important;
    color: var(--iot-primary) !important;
    border: 1px solid rgba(45, 127, 249, 0.2);
}

.feature-tag {
    background: rgba(0, 200, 150, 0.1) !important;
    color: var(--iot-accent) !important;
    border: 1px solid rgba(0, 200, 150, 0.2);
}

.feature-item i {
    color: var(--iot-accent) !important;
}

.plan-price .amount {
    color: var(--iot-accent) !important;
}

.plan-price .currency,
.plan-price .period {
    color: var(--iot-text-muted) !important;
}

.pricing-features .feature i {
    color: var(--iot-accent) !important;
}

.product-name {
    color: var(--iot-accent) !important;
}

.section-title .highlight {
    background: var(--iot-gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Hero section elements */
.iot-hero .hero-badge {
    background: rgba(255, 255, 255, 0.2) !important;
    color: white !important;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.iot-hero .highlight {
    background: var(--iot-gradient-eco);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.iot-hero .stat-number {
    color: var(--iot-accent) !important;
    text-shadow: 0 0 20px rgba(0, 200, 150, 0.4);
}

/* Map preview styling */
.map-preview {
    border: 2px solid var(--iot-accent) !important;
}

.map-placeholder {
    color: var(--iot-primary) !important;
}

/* Footer customization for IoT page */
.footer {
    background: var(--iot-dark) !important;
}

.footer-brand h3 {
    color: var(--iot-primary) !important;
}

.footer-title {
    color: var(--iot-primary) !important;
}

.footer-links a:hover {
    color: var(--iot-accent) !important;
}

.social-link:hover {
    background: var(--iot-accent) !important;
    color: white !important;
}

/* Navbar customization for IoT page */
.navbar .nav-link:hover {
    color: var(--iot-primary) !important;
}

.navbar .nav-button {
    background: var(--iot-accent) !important;
    color: white !important;
}

.navbar .nav-button:hover {
    background: var(--iot-primary) !important;
}

/* Additional elements */
.hero-content {
    text-align: center !important;
}

.section-header {
    text-align: center !important;
}

.plan-name {
    color: var(--iot-accent) !important;
    font-weight: 700;
}

/* EnviroMesh specific styling */
.enviromesh-badge {
    background: var(--iot-gradient-accent) !important;
    color: white !important;
}

/* Responsividade */
@media (max-width: 1200px) {
    .pricing-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
    }

    .product-hero-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }
}

@media (max-width: 768px) {
    .nav-links {
        display: none;
    }

    .mobile-menu-button {
        display: flex;
    }

    .pricing-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .products-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .iot-hero {
        padding: 6rem 0 4rem;
    }

    .iot-hero .hero-title {
        font-size: 2.5rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .sensor-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .iot-dashboard {
        padding: 1rem;
    }

    .map-preview {
        padding: 1.5rem;
    }
}
