/* ===== ASIMOV PRODUCTIVITY STYLES - PURPLE/PINK THEME ===== */

/* Override ASIMOV logo colors to use blue theme */
.navbar .logo-primary,
.footer .logo-primary {
    color: var(--asimov-blue) !important;
}

/* Force center alignment for section titles - Override style.css conflicts */
#docgenie .section-title,
.productivity-products .section-title,
.product-hero-header .section-title,
.pricing-section .section-title,
.products-grid-section .section-title,
.section-title {
    text-align: center !important;
    margin-left: auto !important;
    margin-right: auto !important;
    display: block !important;
    width: 100% !important;
    left: auto !important;
    transform: none !important;
    position: relative !important;
}

/* Ensure containers are also centered */
.product-hero-header,
.productivity-products .section-header,
.pricing-section .section-header,
.products-grid-section .section-header,
.section-header {
    text-align: center !important;
    width: 100% !important;
    display: block !important;
}

:root {
    /* Productivity Color Palette - Professional Blue & Orange Theme */
    --productivity-primary: #2D7FF9;         /* ASIMOV Blue - Primary Professional */
    --productivity-secondary: #1C57B5;       /* Dark Professional Blue */
    --productivity-accent: #FF6B35;          /* Productive Orange - Energy & Focus */
    --productivity-light: #FAFBFC;           /* Professional White Background */
    --productivity-dark: #2C3E50;            /* Corporate Dark Text */
    --productivity-text-light: #FFFFFF;      /* Pure White Text */
    --productivity-text-muted: #7F8C8D;      /* Professional Muted Text */
    --productivity-soft-blue: #74B9FF;       /* Soft Professional Blue */
    --productivity-warm-orange: #FF8C42;     /* Warm Orange for highlights */

    /* ASIMOV Brand Colors - Consistent */
    --asimov-blue: #2D7FF9;                  /* Primary Blue for ASIMOV logo */
    --asimov-blue-dark: #1C57B5;             /* Darker Blue */

    /* Professional Gradients */
    --productivity-gradient-primary: linear-gradient(135deg, #2D7FF9 0%, #1C57B5 100%);
    --productivity-gradient-light: linear-gradient(135deg, #FAFBFC 0%, #E3F2FD 100%);
    --productivity-gradient-accent: linear-gradient(135deg, #FF6B35 0%, #2D7FF9 100%);
    --productivity-gradient-warm: linear-gradient(135deg, #FF8C42 0%, #FF6B35 100%);
}

/* Mobile menu styles for productivity page */
.mobile-menu {
    position: fixed;
    top: 80px;
    left: 0;
    right: 0;
    background: white;
    padding: 1rem;
    border-bottom: 1px solid var(--neutral-200);
    transform: translateY(-100%);
    transition: transform 0.3s ease;
    z-index: 999;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.mobile-menu.active {
    transform: translateY(0);
}

.mobile-nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    color: var(--neutral-700);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border-radius: 8px;
    margin-bottom: 0.5rem;
}

.mobile-nav-link:hover {
    background: var(--primary-light);
    color: var(--primary-color);
}

.mobile-nav-link i {
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

.mobile-menu-button {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
}

.mobile-menu-button span {
    width: 25px;
    height: 3px;
    background: var(--neutral-700);
    border-radius: 2px;
    transition: all 0.3s ease;
}

.mobile-menu-button.active span:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-button.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-button.active span:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* Productivity Hero Section Enhancements */
.productivity-hero {
    background: var(--productivity-gradient-primary);
    position: relative;
    overflow: hidden;
}

.productivity-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="productivity-grid" width="16" height="16" patternUnits="userSpaceOnUse"><circle cx="8" cy="8" r="1.2" fill="rgba(255,107,53,0.2)"/><path d="M 4 8 L 12 8 M 8 4 L 8 12" stroke="rgba(255,107,53,0.1)" stroke-width="0.8"/></pattern></defs><rect width="100" height="100" fill="url(%23productivity-grid)"/></svg>');
    opacity: 0.4;
}

.productivity-hero .hero-description {
    font-size: 1.25rem;
    line-height: 1.6;
    margin-bottom: 3rem;
    max-width: 600px;
    opacity: 1;
    text-align: center;
    margin-left: auto;
    margin-right: auto;
    color: white;
    font-weight: 400;
}

/* Product Hero Section */
.product-hero {
    background: var(--productivity-light);
    padding: 6rem 0;
}

.product-hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

/* DocGenie Interface Enhancements */
.product-preview {
    padding: 2rem;
    background: white;
    border-radius: 16px;
    border: 2px solid var(--productivity-accent);
    box-shadow: 0 10px 30px rgba(255, 107, 53, 0.15);
}

.preview-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: var(--productivity-light);
    border-radius: 12px 12px 0 0;
    border-bottom: 1px solid var(--neutral-200);
}

.preview-controls {
    display: flex;
    gap: 0.5rem;
}

.control {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--neutral-300);
}

.control:nth-child(1) {
    background: #ff5f56;
}

.control:nth-child(2) {
    background: #ffbd2e;
}

.control:nth-child(3) {
    background: #27ca3f;
}

.preview-title {
    font-weight: 600;
    color: var(--productivity-primary);
}

.doc-interface {
    padding: 1.5rem;
}

.doc-toolbar {
    margin-bottom: 1.5rem;
}

.toolbar-section {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.tool-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: var(--productivity-light);
    border: 1px solid var(--neutral-200);
    border-radius: 8px;
    font-size: 0.8rem;
    font-weight: 500;
    color: var(--neutral-700);
    cursor: pointer;
    transition: all 0.3s ease;
}

.tool-btn.active {
    background: var(--productivity-gradient-badge);
    color: white;
    border-color: var(--productivity-secondary);
}

.tool-btn:hover {
    background: var(--productivity-secondary);
    color: white;
}

.doc-content {
    background: var(--productivity-light);
    padding: 1.5rem;
    border-radius: 12px;
    margin-bottom: 1rem;
}

.doc-title {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--productivity-dark);
    margin-bottom: 1rem;
}

.text-line {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--neutral-700);
    font-size: 0.9rem;
}

.text-line.ai-generated {
    color: var(--productivity-secondary);
    font-style: italic;
}

.ai-suggestions {
    background: rgba(102, 126, 234, 0.1);
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid var(--productivity-secondary);
}

.suggestion-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--productivity-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

/* Pricing Section Enhancements */
.pricing-section {
    background: var(--productivity-gradient-light);
    padding: 6rem 0;
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 3rem;
}

.pricing-card {
    background: white;
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(240, 147, 251, 0.2);
}

.pricing-card.featured {
    border: 2px solid var(--productivity-secondary);
    transform: scale(1.05);
}

.pricing-card.featured:hover {
    transform: scale(1.05) translateY(-10px);
}

.pricing-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--productivity-gradient-accent);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

/* Products Grid Section */
.products-grid-section {
    background: white;
    padding: 6rem 0;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.product-card {
    background: white;
    border: 1px solid var(--neutral-200);
    border-radius: 16px;
    padding: 2rem;
    transition: all 0.3s ease;
    position: relative;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(240, 147, 251, 0.15);
    border-color: var(--productivity-secondary);
}

.product-icon {
    width: 60px;
    height: 60px;
    background: var(--productivity-gradient-primary);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.product-icon i {
    font-size: 1.5rem;
    color: white;
}

/* Status badges */
.status-badge.beta {
    background: #ddd6fe;
    color: #5b21b6;
}

.status-badge.active {
    background: #dcfce7;
    color: #166534;
}

.status-badge.planning {
    background: #fef3c7;
    color: #92400e;
}

/* CTA Section */
.cta-section {
    background: var(--productivity-gradient-primary);
    color: white;
    padding: 6rem 0;
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
}

.cta-content p {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Button Styles */
.button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    text-align: center;
    justify-content: center;
}

.button-primary {
    background: var(--productivity-accent);
    color: var(--productivity-primary);
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
}

.button-primary:hover {
    background: var(--productivity-accent-blue);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 193, 7, 0.4);
}

.button-secondary {
    background: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.button-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

.button-outline {
    background: transparent;
    color: var(--productivity-accent);
    border: 2px solid var(--productivity-accent);
}

.button-outline:hover {
    background: var(--productivity-accent);
    color: var(--productivity-primary);
    border-color: var(--productivity-accent);
}

.button i {
    font-size: 1.1rem;
}

/* Pricing Section Button Overrides */
.pricing-card .button-outline {
    background: transparent;
    color: var(--productivity-accent);
    border: 2px solid var(--productivity-accent);
}

.pricing-card .button-outline:hover {
    background: var(--productivity-accent);
    color: var(--productivity-primary);
    border-color: var(--productivity-accent);
}

.pricing-card .button-primary {
    background: var(--productivity-accent);
    color: var(--productivity-primary);
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
}

.pricing-card .button-primary:hover {
    background: var(--productivity-accent-blue);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 193, 7, 0.4);
}

/* Responsividade */
@media (max-width: 1200px) {
    .pricing-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
    }

    .product-hero-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }
}

@media (max-width: 768px) {
    .nav-links {
        display: none;
    }

    .mobile-menu-button {
        display: flex;
    }

    .pricing-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .products-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .productivity-hero {
        padding: 6rem 0 4rem;
    }

    .productivity-hero .hero-title {
        font-size: 2.5rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .toolbar-section {
        flex-wrap: wrap;
        gap: 0.25rem;
    }

    .tool-btn {
        font-size: 0.7rem;
        padding: 0.4rem 0.8rem;
    }

    .doc-interface {
        padding: 1rem;
    }

    .doc-content {
        padding: 1rem;
    }

    .product-preview {
        padding: 1rem;
    }
}
